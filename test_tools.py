"""
测试审计报告分析工具
验证工具功能是否正常
"""

import os
import pandas as pd
from audit_tools import (
    list_excel_files,
    list_sheets,
    view_sheet,
    search_sheet,
    get_sheet_summary
)


def create_test_excel():
    """创建测试用的 Excel 文件"""
    # 确保目录存在
    excel_dir = "./excel_files"
    if not os.path.exists(excel_dir):
        os.makedirs(excel_dir)
    
    # 创建示例数据
    # 资产负债表
    balance_sheet_data = {
        '科目': ['货币资金', '应收账款', '存货', '固定资产', '应付账款', '短期借款', '实收资本', '未分配利润'],
        '期末余额': [1000000, 800000, 600000, 2000000, 500000, 300000, 2000000, 1600000],
        '期初余额': [900000, 750000, 550000, 1900000, 480000, 350000, 2000000, 1370000],
        '备注': ['银行存款', '客户欠款', '库存商品', '设备等', '供应商欠款', '银行贷款', '股东投资', '累计盈余']
    }
    
    # 利润表
    income_statement_data = {
        '科目': ['营业收入', '营业成本', '销售费用', '管理费用', '财务费用', '营业利润', '净利润'],
        '本期金额': [5000000, 3000000, 500000, 400000, 100000, 1000000, 800000],
        '上期金额': [4500000, 2800000, 450000, 380000, 120000, 750000, 600000],
        '增减额': [500000, 200000, 50000, 20000, -20000, 250000, 200000]
    }
    
    # 现金流量表
    cashflow_data = {
        '项目': ['销售商品收到的现金', '购买商品支付的现金', '支付职工薪酬', '投资活动现金流入', '投资活动现金流出', '筹资活动现金流入'],
        '金额': [4800000, -2900000, -800000, 200000, -500000, 1000000],
        '说明': ['主营业务收入', '采购支出', '人工成本', '投资收益', '设备采购', '银行借款']
    }
    
    # 创建 Excel 文件
    test_file = os.path.join(excel_dir, "测试审计报告2024.xlsx")
    
    try:
        with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
            pd.DataFrame(balance_sheet_data).to_excel(writer, sheet_name='资产负债表', index=False)
            pd.DataFrame(income_statement_data).to_excel(writer, sheet_name='利润表', index=False)
            pd.DataFrame(cashflow_data).to_excel(writer, sheet_name='现金流量表', index=False)
        
        print(f"✅ 已创建测试 Excel 文件: {test_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试文件失败: {e}")
        return False


def test_tools():
    """测试所有工具功能"""
    print("🧪 开始测试审计报告分析工具...")
    print("=" * 60)
    
    # 1. 测试列出文件
    print("\n1️⃣ 测试 list_excel_files 工具:")
    print("-" * 40)
    result = list_excel_files()
    print(result)
    
    # 2. 测试列出工作表
    print("\n2️⃣ 测试 list_sheets 工具:")
    print("-" * 40)
    result = list_sheets("测试审计报告2024.xlsx")
    print(result)
    
    # 3. 测试查看工作表内容
    print("\n3️⃣ 测试 view_sheet 工具:")
    print("-" * 40)
    result = view_sheet("测试审计报告2024.xlsx", "资产负债表", max_rows=10, max_cols=5)
    print(result)
    
    # 4. 测试搜索功能
    print("\n4️⃣ 测试 search_sheet 工具:")
    print("-" * 40)
    result = search_sheet("测试审计报告2024.xlsx", "资产负债表", "应收账款", max_results=5)
    print(result)
    
    # 5. 测试获取摘要
    print("\n5️⃣ 测试 get_sheet_summary 工具:")
    print("-" * 40)
    result = get_sheet_summary("测试审计报告2024.xlsx", "利润表")
    print(result)
    
    print("\n" + "=" * 60)
    print("🎉 工具测试完成！")


def test_agent():
    """测试完整的 Agent"""
    print("\n🤖 测试审计报告分析 Agent...")
    print("=" * 60)
    
    try:
        from audit_agent import AuditReportAgent
        
        # 创建 Agent
        agent = AuditReportAgent()
        
        print("✅ Agent 创建成功！")
        print(f"📁 Excel 文件目录: {agent.excel_directory}")
        print(f"🔧 工具数量: {len(agent.tools)}")
        
        # 测试简单对话
        print("\n💬 测试 Agent 对话功能...")
        response = agent.start("你好，请列出可用的审计报告文件")
        
        print("🎉 Agent 测试完成！")
        
    except Exception as e:
        print(f"❌ Agent 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("🎯 审计报告分析工具测试程序")
    print("=" * 60)
    
    # 检查依赖
    try:
        import pandas
        import openpyxl
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return
    
    # 创建测试文件
    print("\n📁 创建测试 Excel 文件...")
    if not create_test_excel():
        print("❌ 无法创建测试文件，跳过工具测试")
        return
    
    # 测试工具
    test_tools()
    
    # 测试 Agent
    test_agent()


if __name__ == "__main__":
    main()
