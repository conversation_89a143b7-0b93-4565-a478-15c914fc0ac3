"""
审计报告分析 Agent 使用示例
"""

import os
from audit_agent import AuditReportAgent


def interactive_demo():
    """交互式演示"""
    print("=" * 60)
    print("🎯 审计报告分析助手 - 交互式演示")
    print("=" * 60)
    
    # 创建审计助手
    agent = AuditReportAgent()
    
    print("\n📋 可用命令示例：")
    print("1. '列出所有文件' - 查看可用的 Excel 文件")
    print("2. '查看文件中的工作表' - 显示指定文件的所有工作表")
    print("3. '查看资产负债表' - 显示资产负债表内容")
    print("4. '搜索应收账款' - 在表格中搜索特定科目")
    print("5. '获取表格摘要' - 显示表格的统计信息")
    print("6. 'quit' - 退出程序")
    
    # 启动助手
    agent.start()
    
    # 交互循环
    while True:
        try:
            user_input = input("\n💬 请输入您的问题 (输入 'quit' 退出): ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 感谢使用审计报告分析助手！")
                break
            
            if not user_input:
                continue
            
            print("\n🤖 助手回复:")
            print("-" * 50)
            
            # 发送消息给助手
            response = agent.chat(user_input)
            
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")


def batch_demo():
    """批量演示常用功能"""
    print("=" * 60)
    print("🎯 审计报告分析助手 - 批量演示")
    print("=" * 60)
    
    # 创建审计助手
    agent = AuditReportAgent()
    
    # 演示命令列表
    demo_commands = [
        "列出所有可用的审计报告文件",
        "如果有文件的话，显示第一个文件中的所有工作表",
        "帮我查看资产负债表的内容",
        "在资产负债表中搜索'应收账款'相关的数据",
        "给我一个资产负债表的数据摘要"
    ]
    
    print(f"\n🚀 开始演示 {len(demo_commands)} 个常用功能...")
    
    for i, command in enumerate(demo_commands, 1):
        print(f"\n{'='*20} 演示 {i}/{len(demo_commands)} {'='*20}")
        print(f"📝 命令: {command}")
        print("\n🤖 助手回复:")
        print("-" * 50)
        
        try:
            if i == 1:
                # 第一次启动
                agent.start(command)
            else:
                # 后续对话
                agent.chat(command)
            
            print("-" * 50)
            
            # 等待用户按键继续
            input("\n⏸️  按 Enter 继续下一个演示...")
            
        except Exception as e:
            print(f"❌ 演示 {i} 出错: {e}")


def create_sample_excel():
    """创建示例 Excel 文件"""
    try:
        import pandas as pd
        
        # 确保目录存在
        excel_dir = "./excel_files"
        if not os.path.exists(excel_dir):
            os.makedirs(excel_dir)
        
        # 创建示例数据
        # 资产负债表
        balance_sheet_data = {
            '科目': ['货币资金', '应收账款', '存货', '固定资产', '应付账款', '短期借款', '实收资本', '未分配利润'],
            '期末余额': [1000000, 800000, 600000, 2000000, 500000, 300000, 2000000, 1600000],
            '期初余额': [900000, 750000, 550000, 1900000, 480000, 350000, 2000000, 1370000],
            '备注': ['银行存款', '客户欠款', '库存商品', '设备等', '供应商欠款', '银行贷款', '股东投资', '累计盈余']
        }
        
        # 利润表
        income_statement_data = {
            '科目': ['营业收入', '营业成本', '销售费用', '管理费用', '财务费用', '营业利润', '净利润'],
            '本期金额': [5000000, 3000000, 500000, 400000, 100000, 1000000, 800000],
            '上期金额': [4500000, 2800000, 450000, 380000, 120000, 750000, 600000],
            '增减额': [500000, 200000, 50000, 20000, -20000, 250000, 200000]
        }
        
        # 现金流量表
        cashflow_data = {
            '项目': ['销售商品收到的现金', '购买商品支付的现金', '支付职工薪酬', '投资活动现金流入', '投资活动现金流出', '筹资活动现金流入'],
            '金额': [4800000, -2900000, -800000, 200000, -500000, 1000000],
            '说明': ['主营业务收入', '采购支出', '人工成本', '投资收益', '设备采购', '银行借款']
        }
        
        # 创建 Excel 文件
        sample_file = os.path.join(excel_dir, "示例审计报告2024.xlsx")
        
        with pd.ExcelWriter(sample_file, engine='openpyxl') as writer:
            pd.DataFrame(balance_sheet_data).to_excel(writer, sheet_name='资产负债表', index=False)
            pd.DataFrame(income_statement_data).to_excel(writer, sheet_name='利润表', index=False)
            pd.DataFrame(cashflow_data).to_excel(writer, sheet_name='现金流量表', index=False)
        
        print(f"✅ 已创建示例 Excel 文件: {sample_file}")
        return True
        
    except ImportError:
        print("❌ 需要安装 pandas 和 openpyxl 库来创建示例文件")
        print("请运行: pip install pandas openpyxl")
        return False
    except Exception as e:
        print(f"❌ 创建示例文件失败: {e}")
        return False


def main():
    """主函数"""
    print("🎯 审计报告分析助手 - 使用示例")
    print("\n请选择运行模式：")
    print("1. 交互式演示 (推荐)")
    print("2. 批量功能演示")
    print("3. 创建示例 Excel 文件")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                interactive_demo()
                break
            elif choice == '2':
                batch_demo()
                break
            elif choice == '3':
                if create_sample_excel():
                    print("\n✅ 示例文件创建完成！现在可以运行演示了。")
                    continue
                else:
                    break
            elif choice == '4':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break


if __name__ == "__main__":
    main()
