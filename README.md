# 审计报告分析 Agent

基于 PraisonAI 框架的智能审计报告分析助手，可以帮助您轻松分析和查看 Excel 格式的审计报告。

## ✨ 功能特性

- 📁 **文件管理**: 自动识别和列出可用的 Excel 审计报告文件
- 📊 **工作表浏览**: 查看 Excel 文件中的所有工作表（资产负债表、利润表等）
- 📋 **内容查看**: 以表格形式显示指定工作表的详细内容
- 🔍 **智能搜索**: 在工作表中搜索特定的财务数据或科目
- 📈 **数据摘要**: 提供工作表的统计摘要和数据概览
- 🤖 **智能对话**: 支持自然语言交互，理解用户意图

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置 API 密钥

设置您的 LLM API 密钥（以 DeepSeek 为例）：

```bash
export OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
export OPENAI_API_KEY=your-api-key-here
```

### 3. 准备 Excel 文件

将您的审计报告 Excel 文件放入 `excel_files` 目录中：

```
excel_files/
├── 公司A审计报告2024.xlsx
├── 公司B审计报告2023.xlsx
└── 财务报表汇总.xlsx
```

### 4. 测试工具

首先运行测试脚本验证功能：

```bash
python test_tools.py
```

### 5. 运行助手

#### 方式一：直接运行主程序
```bash
python audit_agent.py
```

#### 方式二：运行交互式演示
```bash
python example_usage.py
```

## 📖 使用指南

### 基本工作流程

1. **查看可用文件**
   ```
   用户: "列出所有审计报告文件"
   助手: 显示 excel_files 目录中的所有 Excel 文件
   ```

2. **选择文件和工作表**
   ```
   用户: "查看公司A审计报告2024.xlsx中的工作表"
   助手: 显示该文件中的所有工作表名称
   ```

3. **查看具体内容**
   ```
   用户: "显示资产负债表的内容"
   助手: 以表格形式展示资产负债表数据
   ```

4. **搜索特定信息**
   ```
   用户: "在资产负债表中搜索应收账款"
   助手: 显示包含"应收账款"的所有行
   ```

5. **获取数据摘要**
   ```
   用户: "给我资产负债表的摘要信息"
   助手: 提供数据统计、列信息等概览
   ```

### 支持的命令类型

- **文件操作**: "列出文件"、"显示所有Excel文件"
- **工作表操作**: "查看工作表"、"显示表格列表"
- **内容查看**: "查看资产负债表"、"显示利润表内容"
- **搜索功能**: "搜索应收账款"、"查找固定资产"
- **数据分析**: "获取摘要"、"统计信息"

## 🛠️ 技术架构

### 核心组件

1. **ExcelProcessor** (`excel_tools.py`)
   - Excel 文件读取和解析
   - 数据处理和格式化
   - 搜索和统计功能

2. **PraisonAI 函数式工具** (`audit_tools.py`)
   - `list_excel_files`: 列出可用文件
   - `list_sheets`: 显示工作表
   - `view_sheet`: 查看表格内容
   - `search_sheet`: 搜索功能
   - `get_sheet_summary`: 数据摘要

3. **AuditReportAgent** (`audit_agent.py`)
   - 主要的 AI 助手
   - 集成所有工具
   - 处理用户交互

### 工具详细说明

#### list_excel_files()
- **功能**: 扫描 excel_files 目录，列出所有 .xlsx 和 .xls 文件
- **参数**: 无
- **输出**: 文件列表和数量统计

#### list_sheets(filename)
- **参数**: filename (Excel文件名)
- **功能**: 读取指定文件的所有工作表名称
- **输出**: 工作表列表

#### view_sheet(filename, sheet_name, max_rows, max_cols)
- **参数**:
  - filename: Excel文件名
  - sheet_name: 工作表名称
  - max_rows: 最大显示行数 (默认20)
  - max_cols: 最大显示列数 (默认10)
- **功能**: 显示工作表内容
- **输出**: 格式化的表格数据

#### search_sheet(filename, sheet_name, search_term, max_results)
- **参数**:
  - filename: Excel文件名
  - sheet_name: 工作表名称
  - search_term: 搜索关键词
  - max_results: 最大结果数 (默认10)
- **功能**: 在工作表中搜索包含关键词的行
- **输出**: 匹配的数据行

#### get_sheet_summary(filename, sheet_name)
- **参数**:
  - filename: Excel文件名
  - sheet_name: 工作表名称
- **功能**: 生成工作表的统计摘要
- **输出**: 数据规模、列信息、数值统计等

## 📝 配置说明

### API 配置

在 `audit_agent.py` 中配置您的 LLM API：

```python
# 设置 API 配置
os.environ['OPENAI_BASE_URL'] = 'your-api-base-url'
os.environ['OPENAI_API_KEY'] = 'your-api-key'
```

### 目录配置

默认 Excel 文件目录为 `./excel_files`，可以在创建 Agent 时自定义：

```python
agent = AuditReportAgent(excel_directory="./my_excel_files")
```

## 🔧 自定义扩展

### 添加新工具

1. 在 `audit_tools.py` 中创建新的工具函数：

```python
def my_custom_tool(param1: str) -> str:
    """
    我的自定义工具

    Args:
        param1: 参数说明

    Returns:
        str: 工具执行结果
    """
    # 实现工具逻辑
    return "工具执行结果"
```

2. 在 `AuditReportAgent` 中添加工具：

```python
self.tools.append(my_custom_tool)
```

### 修改显示格式

在各个工具的 `_run` 方法中修改输出格式，支持：
- 表格格式
- JSON 格式
- 自定义格式

## 🐛 故障排除

### 常见问题

1. **找不到 Excel 文件**
   - 确保文件放在正确的目录中
   - 检查文件扩展名是否为 .xlsx 或 .xls

2. **工作表读取失败**
   - 确保 Excel 文件没有密码保护
   - 检查工作表名称是否正确

3. **依赖包安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

4. **API 连接问题**
   - 检查网络连接
   - 验证 API 密钥和端点配置

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请创建 Issue 或联系开发团队。
