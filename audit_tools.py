"""
审计报告分析工具
基于 PraisonAI 框架的工具集合
"""

from praisonaiagents.tools import BaseTool
from excel_tools import ExcelProcessor
import json
from typing import Dict, Any, List


class ListExcelFilesTool(BaseTool):
    """列出可用的 Excel 文件工具"""
    
    name: str = "list_excel_files"
    description: str = "列出当前目录中所有可用的审计报告 Excel 文件"
    
    def __init__(self, excel_directory: str = "./excel_files"):
        super().__init__()
        self.processor = ExcelProcessor(excel_directory)
    
    def _run(self) -> str:
        """执行工具"""
        try:
            files = self.processor.get_excel_files()
            
            if not files:
                return "当前目录中没有找到 Excel 文件。请确保将审计报告文件放在 excel_files 目录中。"
            
            result = "📁 可用的审计报告 Excel 文件：\n\n"
            for i, file in enumerate(files, 1):
                result += f"{i}. {file}\n"
            
            result += f"\n共找到 {len(files)} 个文件。"
            result += "\n\n💡 使用 'list_sheets' 工具查看文件中的工作表。"
            
            return result
            
        except Exception as e:
            return f"❌ 获取文件列表失败: {str(e)}"


class ListSheetsTool(BaseTool):
    """列出 Excel 文件中工作表的工具"""
    
    name: str = "list_sheets"
    description: str = "列出指定 Excel 文件中的所有工作表名称"
    
    def __init__(self, excel_directory: str = "./excel_files"):
        super().__init__()
        self.processor = ExcelProcessor(excel_directory)
    
    def _run(self, filename: str) -> str:
        """
        执行工具
        
        Args:
            filename: Excel 文件名
        """
        try:
            sheets = self.processor.get_sheet_names(filename)
            
            result = f"📊 文件 '{filename}' 中的工作表：\n\n"
            for i, sheet in enumerate(sheets, 1):
                result += f"{i}. {sheet}\n"
            
            result += f"\n共找到 {len(sheets)} 个工作表。"
            result += "\n\n💡 使用 'view_sheet' 工具查看具体工作表内容。"
            
            return result
            
        except Exception as e:
            return f"❌ 获取工作表列表失败: {str(e)}"


class ViewSheetTool(BaseTool):
    """查看工作表内容的工具"""
    
    name: str = "view_sheet"
    description: str = "查看指定 Excel 文件中指定工作表的内容"
    
    def __init__(self, excel_directory: str = "./excel_files"):
        super().__init__()
        self.processor = ExcelProcessor(excel_directory)
    
    def _run(self, filename: str, sheet_name: str, max_rows: int = 20, max_cols: int = 10) -> str:
        """
        执行工具
        
        Args:
            filename: Excel 文件名
            sheet_name: 工作表名称
            max_rows: 最大显示行数 (默认20)
            max_cols: 最大显示列数 (默认10)
        """
        try:
            data = self.processor.read_sheet_data(filename, sheet_name, max_rows, max_cols)
            
            result = f"📋 工作表内容: {filename} - {sheet_name}\n"
            result += f"📏 总大小: {data['total_rows']} 行 × {data['total_cols']} 列\n"
            result += f"👀 {data['preview_note']}\n\n"
            
            # 显示列名
            result += "📊 列名:\n"
            for i, col in enumerate(data['columns'], 1):
                result += f"  {i}. {col}\n"
            result += "\n"
            
            # 显示数据
            result += "📄 数据内容:\n"
            if data['data']:
                # 创建表格格式
                result += "| " + " | ".join([str(col)[:15] for col in data['columns']]) + " |\n"
                result += "|" + "|".join(["-" * 17 for _ in data['columns']]) + "|\n"
                
                for row in data['data'][:10]:  # 只显示前10行
                    row_data = []
                    for col in data['columns']:
                        cell_value = str(row.get(col, ''))[:15]
                        row_data.append(cell_value)
                    result += "| " + " | ".join(row_data) + " |\n"
                
                if len(data['data']) > 10:
                    result += f"\n... 还有 {len(data['data']) - 10} 行数据\n"
            else:
                result += "该工作表为空。\n"
            
            result += f"\n💡 使用 'search_sheet' 工具在此工作表中搜索特定内容。"
            
            return result
            
        except Exception as e:
            return f"❌ 查看工作表失败: {str(e)}"


class SearchSheetTool(BaseTool):
    """在工作表中搜索内容的工具"""
    
    name: str = "search_sheet"
    description: str = "在指定工作表中搜索包含特定关键词的内容"
    
    def __init__(self, excel_directory: str = "./excel_files"):
        super().__init__()
        self.processor = ExcelProcessor(excel_directory)
    
    def _run(self, filename: str, sheet_name: str, search_term: str, max_results: int = 10) -> str:
        """
        执行工具
        
        Args:
            filename: Excel 文件名
            sheet_name: 工作表名称
            search_term: 搜索关键词
            max_results: 最大结果数 (默认10)
        """
        try:
            results = self.processor.search_in_sheet(filename, sheet_name, search_term, max_results)
            
            result = f"🔍 搜索结果: '{search_term}' 在 {filename} - {sheet_name}\n"
            result += f"📊 找到 {results['total_matches']} 个匹配项，显示前 {results['displayed_matches']} 个\n\n"
            
            if results['matching_data']:
                result += "📄 匹配的数据:\n"
                # 创建表格格式
                cols = results['columns'][:8]  # 只显示前8列
                result += "| " + " | ".join([str(col)[:12] for col in cols]) + " |\n"
                result += "|" + "|".join(["-" * 14 for _ in cols]) + "|\n"
                
                for row in results['matching_data']:
                    row_data = []
                    for col in cols:
                        cell_value = str(row.get(col, ''))[:12]
                        row_data.append(cell_value)
                    result += "| " + " | ".join(row_data) + " |\n"
            else:
                result += "❌ 没有找到匹配的内容。\n"
            
            return result
            
        except Exception as e:
            return f"❌ 搜索失败: {str(e)}"


class GetSheetSummaryTool(BaseTool):
    """获取工作表摘要信息的工具"""
    
    name: str = "get_sheet_summary"
    description: str = "获取指定工作表的摘要信息，包括数据类型、统计信息等"
    
    def __init__(self, excel_directory: str = "./excel_files"):
        super().__init__()
        self.processor = ExcelProcessor(excel_directory)
    
    def _run(self, filename: str, sheet_name: str) -> str:
        """
        执行工具
        
        Args:
            filename: Excel 文件名
            sheet_name: 工作表名称
        """
        try:
            summary = self.processor.get_sheet_summary(filename, sheet_name)
            
            result = f"📊 工作表摘要: {filename} - {sheet_name}\n\n"
            result += f"📏 数据规模: {summary['total_rows']} 行 × {summary['total_cols']} 列\n"
            result += f"🔢 包含数值数据: {'是' if summary['has_numeric_data'] else '否'}\n\n"
            
            result += "📋 列信息:\n"
            for i, col in enumerate(summary['columns'], 1):
                non_null = summary['non_null_counts'].get(col, 0)
                result += f"  {i}. {col} (非空值: {non_null})\n"
            
            result += "\n📄 数据预览 (前3行):\n"
            if summary['first_few_rows']:
                for i, row in enumerate(summary['first_few_rows'], 1):
                    result += f"  行{i}: "
                    row_preview = []
                    for col in summary['columns'][:5]:  # 只显示前5列
                        value = str(row.get(col, ''))[:20]
                        row_preview.append(f"{col}={value}")
                    result += ", ".join(row_preview) + "\n"
            
            # 如果有数值统计信息
            if 'numeric_summary' in summary:
                result += "\n📈 数值列统计:\n"
                for col, stats in summary['numeric_summary'].items():
                    if isinstance(stats, dict) and 'mean' in stats:
                        result += f"  {col}: 平均值={stats['mean']:.2f}, 最大值={stats['max']:.2f}, 最小值={stats['min']:.2f}\n"
            
            return result
            
        except Exception as e:
            return f"❌ 获取摘要失败: {str(e)}"
