

import os
os.environ['OPENAI_BASE_URL'] = 'https://ark.cn-beijing.volces.com/api/v3'
os.environ['OPENAI_API_KEY'] = '8bebb1cf-7879-47c7-a258-abf38368d682'
"""
审计报告分析 Agent
基于 PraisonAI 框架的智能审计助手
"""
from praisonaiagents import Agent, Task, PraisonAIAgents
from audit_tools import (
    list_excel_files,
    list_sheets,
    view_sheet,
    search_sheet,
    get_sheet_summary
)


class AuditReportAgent:
    """审计报告分析智能助手"""
    
    def __init__(self, excel_directory: str = "./excel_files"):
        """
        初始化审计报告分析 Agent
        
        Args:
            excel_directory: Excel 文件存放目录
        """
        self.excel_directory = excel_directory
        
        # 确保 Excel 文件目录存在
        if not os.path.exists(excel_directory):
            os.makedirs(excel_directory)
            print(f"✅ 已创建 Excel 文件目录: {excel_directory}")
            print(f"📁 请将您的审计报告 Excel 文件放入此目录中")
        
        # 初始化工具 - 使用函数式工具
        self.tools = [
            list_excel_files,
            list_sheets,
            view_sheet,
            search_sheet,
            get_sheet_summary
        ]

        # 创建 Agent
        self.agent = Agent(
            instructions=self._get_instructions(),
            tools=self.tools,
            llm="deepseek-v3-250324",  # 使用您配置的模型
        )
    
    def _get_instructions(self) -> str:
        """获取 Agent 指令"""
        return """
你是一个专业的审计报告分析助手，专门帮助用户分析和查看 Excel 格式的审计报告。
"""
    
    def start(self, initial_message: str = None):
        """
        启动 Agent
        
        Args:
            initial_message: 初始消息
        """

        
        print("🚀 启动审计报告分析助手...")
        print(f"📁 Excel 文件目录: {self.excel_directory}")
        
        # 检查是否有 Excel 文件
        files = []
        if os.path.exists(self.excel_directory):
            files = [f for f in os.listdir(self.excel_directory) 
                    if f.lower().endswith(('.xlsx', '.xls'))]
        
        if files:
            print(f"✅ 发现 {len(files)} 个 Excel 文件")
        else:
            print("⚠️  未发现 Excel 文件，请将审计报告文件放入 excel_files 目录")
        
        return self.agent.start(initial_message)
    
    def chat(self, message: str):
        """
        与 Agent 对话
        
        Args:
            message: 用户消息
        """
        return self.agent.start(message)


def main():
    """主函数"""


    
    # 创建并启动审计助手
    audit_agent = AuditReportAgent()
    # audit_agent.start("找到文件夹里第一个文件，然后查看这个文件里有哪些表")
    pa=PraisonAIAgents(
        agents=[audit_agent.agent],
        verbose=True
    )
    pa.start("找到文件夹里第一个文件，然后查看这个文件里有哪些表")


if __name__ == "__main__":
    main()
