os.environ['OPENAI_BASE_URL'] = 'https://ark.cn-beijing.volces.com/api/v3'
os.environ['OPENAI_API_KEY'] = '8bebb1cf-7879-47c7-a258-abf38368d682'
"""
审计报告分析 Agent
基于 PraisonAI 框架的智能审计助手
"""

import os
from praisonaiagents import Agent
from audit_tools import (
    ListExcelFilesTool,
    ListSheetsTool, 
    ViewSheetTool,
    SearchSheetTool,
    GetSheetSummaryTool
)


class AuditReportAgent:
    """审计报告分析智能助手"""
    
    def __init__(self, excel_directory: str = "./excel_files"):
        """
        初始化审计报告分析 Agent
        
        Args:
            excel_directory: Excel 文件存放目录
        """
        self.excel_directory = excel_directory
        
        # 确保 Excel 文件目录存在
        if not os.path.exists(excel_directory):
            os.makedirs(excel_directory)
            print(f"✅ 已创建 Excel 文件目录: {excel_directory}")
            print(f"📁 请将您的审计报告 Excel 文件放入此目录中")
        
        # 初始化工具
        self.tools = [
            ListExcelFilesTool(excel_directory),
            ListSheetsTool(excel_directory),
            ViewSheetTool(excel_directory),
            SearchSheetTool(excel_directory),
            GetSheetSummaryTool(excel_directory)
        ]
        
        # 创建 Agent
        self.agent = Agent(
            name="审计报告分析助手",
            instructions=self._get_instructions(),
            tools=self.tools,
            llm="deepseek-v3-250324",  # 使用您配置的模型
        )
    
    def _get_instructions(self) -> str:
        """获取 Agent 指令"""
        return """
你是一个专业的审计报告分析助手，专门帮助用户分析和查看 Excel 格式的审计报告。

## 你的能力：
1. 📁 **文件管理**: 列出可用的 Excel 审计报告文件
2. 📊 **工作表浏览**: 查看 Excel 文件中的所有工作表（如资产负债表、利润表等）
3. 📋 **内容查看**: 显示指定工作表的详细内容
4. 🔍 **智能搜索**: 在工作表中搜索特定的财务数据或科目
5. 📈 **数据摘要**: 提供工作表的统计摘要和数据概览

## 可用工具：
- `list_excel_files`: 列出所有可用的 Excel 文件
- `list_sheets`: 显示指定文件中的所有工作表
- `view_sheet`: 查看指定工作表的内容
- `search_sheet`: 在工作表中搜索特定内容
- `get_sheet_summary`: 获取工作表的摘要信息

## 工作流程建议：
1. 首先使用 `list_excel_files` 查看可用文件
2. 选择文件后使用 `list_sheets` 查看工作表
3. 使用 `view_sheet` 查看具体表格内容
4. 根据需要使用 `search_sheet` 搜索特定信息
5. 使用 `get_sheet_summary` 获取数据概览

## 交流风格：
- 使用中文与用户交流
- 提供清晰、专业的财务数据解释
- 主动建议下一步操作
- 用表格和结构化格式展示数据
- 使用表情符号增强可读性

## 注意事项：
- 保护数据隐私，不泄露敏感财务信息
- 提供准确的数据解读
- 如果数据异常，及时提醒用户
- 支持常见的审计报告格式（资产负债表、利润表、现金流量表等）

开始时，请主动询问用户需要分析哪个审计报告文件，并引导用户完成分析流程。
"""
    
    def start(self, initial_message: str = None):
        """
        启动 Agent
        
        Args:
            initial_message: 初始消息
        """
        if initial_message is None:
            initial_message = """
🎯 欢迎使用审计报告分析助手！

我可以帮助您分析 Excel 格式的审计报告，包括：
• 📊 资产负债表
• 💰 利润表  
• 💸 现金流量表
• 📋 其他财务报表

让我们开始吧！请告诉我您想要：
1. 查看可用的审计报告文件
2. 分析特定的财务报表
3. 搜索特定的财务数据

您希望我帮您做什么？
"""
        
        print("🚀 启动审计报告分析助手...")
        print(f"📁 Excel 文件目录: {self.excel_directory}")
        
        # 检查是否有 Excel 文件
        files = []
        if os.path.exists(self.excel_directory):
            files = [f for f in os.listdir(self.excel_directory) 
                    if f.lower().endswith(('.xlsx', '.xls'))]
        
        if files:
            print(f"✅ 发现 {len(files)} 个 Excel 文件")
        else:
            print("⚠️  未发现 Excel 文件，请将审计报告文件放入 excel_files 目录")
        
        return self.agent.start(initial_message)
    
    def chat(self, message: str):
        """
        与 Agent 对话
        
        Args:
            message: 用户消息
        """
        return self.agent.start(message)


def main():
    """主函数"""
    # 设置 API 配置（如果需要）


    
    # 创建并启动审计助手
    audit_agent = AuditReportAgent()
    audit_agent.start()


if __name__ == "__main__":
    main()
