import os
os.environ['OPENAI_BASE_URL'] = 'https://ark.cn-beijing.volces.com/api/v3'
os.environ['OPENAI_API_KEY'] = '8bebb1cf-7879-47c7-a258-abf38368d682'
from camel.agents import ChatAgent
from camel.toolkits import FunctionTool

# Define a tool
def calculator(a: int, b: int) -> int:
    return a + b

# Create agent with tool
agent = ChatAgent(tools=[calculator])

# The agent can now use the calculator tool in conversations
response = agent.step("What is 5 + 3?")