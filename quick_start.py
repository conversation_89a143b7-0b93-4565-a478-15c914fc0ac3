#!/usr/bin/env python3
"""
审计报告分析 Agent 快速启动脚本
"""

import os
import sys


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'praisonaiagents',
        'pandas',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True


def check_api_key():
    """检查 API 密钥配置"""
    api_key = os.getenv('OPENAI_API_KEY')
    base_url = os.getenv('OPENAI_BASE_URL')
    
    if not api_key:
        print("⚠️  未设置 OPENAI_API_KEY 环境变量")
        print("请设置您的 API 密钥:")
        print("export OPENAI_API_KEY=your-api-key-here")
        
        # 尝试使用默认配置
        print("\n🔧 使用默认 DeepSeek 配置...")
        os.environ['OPENAI_BASE_URL'] = 'https://ark.cn-beijing.volces.com/api/v3'
        os.environ['OPENAI_API_KEY'] = '8bebb1cf-7879-47c7-a258-abf38368d682'
        print("✅ 已设置默认 API 配置")
        return True
    
    print("✅ API 密钥配置正常")
    if base_url:
        print(f"🔗 API 端点: {base_url}")
    return True


def check_excel_files():
    """检查 Excel 文件"""
    excel_dir = "./excel_files"
    
    if not os.path.exists(excel_dir):
        os.makedirs(excel_dir)
        print(f"📁 已创建 Excel 文件目录: {excel_dir}")
    
    excel_files = []
    if os.path.exists(excel_dir):
        excel_files = [f for f in os.listdir(excel_dir) 
                      if f.lower().endswith(('.xlsx', '.xls'))]
    
    if excel_files:
        print(f"📊 发现 {len(excel_files)} 个 Excel 文件:")
        for file in excel_files:
            print(f"   - {file}")
    else:
        print("📂 未发现 Excel 文件")
        print("💡 您可以:")
        print("   1. 将 Excel 文件放入 excel_files 目录")
        print("   2. 运行 test_tools.py 创建示例文件")
    
    return True


def create_sample_data():
    """创建示例数据"""
    try:
        from test_tools import create_test_excel
        print("\n🔧 创建示例 Excel 文件...")
        if create_test_excel():
            print("✅ 示例文件创建成功")
            return True
        else:
            print("❌ 示例文件创建失败")
            return False
    except Exception as e:
        print(f"❌ 创建示例文件时出错: {e}")
        return False


def start_agent():
    """启动 Agent"""
    try:
        print("\n🚀 启动审计报告分析 Agent...")
        from audit_agent import AuditReportAgent
        
        agent = AuditReportAgent()
        agent.start()
        
    except Exception as e:
        print(f"❌ 启动 Agent 失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def main():
    """主函数"""
    print("🎯 审计报告分析 Agent - 快速启动")
    print("=" * 50)
    
    # 1. 检查依赖
    print("\n1️⃣ 检查依赖...")
    if not check_dependencies():
        sys.exit(1)
    
    # 2. 检查 API 配置
    print("\n2️⃣ 检查 API 配置...")
    if not check_api_key():
        sys.exit(1)
    
    # 3. 检查 Excel 文件
    print("\n3️⃣ 检查 Excel 文件...")
    check_excel_files()
    
    # 4. 询问是否创建示例数据
    excel_files = []
    excel_dir = "./excel_files"
    if os.path.exists(excel_dir):
        excel_files = [f for f in os.listdir(excel_dir) 
                      if f.lower().endswith(('.xlsx', '.xls'))]
    
    if not excel_files:
        print("\n❓ 是否创建示例 Excel 文件用于测试？")
        choice = input("输入 y/yes 创建示例文件，或按 Enter 跳过: ").strip().lower()
        
        if choice in ['y', 'yes']:
            create_sample_data()
    
    # 5. 启动 Agent
    print("\n4️⃣ 启动 Agent...")
    start_agent()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()
