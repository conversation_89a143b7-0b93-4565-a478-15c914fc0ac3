"""
Excel 处理工具模块
用于处理审计报告 Excel 文件的读取和分析
"""

import pandas as pd
import os
from typing import List, Dict, Any, Optional
import json


class ExcelProcessor:
    """Excel 文件处理器"""
    
    def __init__(self, excel_directory: str = "./excel_files"):
        """
        初始化 Excel 处理器
        
        Args:
            excel_directory: Excel 文件存放目录
        """
        self.excel_directory = excel_directory
        self.supported_extensions = ['.xlsx', '.xls']
        
    def get_excel_files(self) -> List[str]:
        """
        获取目录中所有的 Excel 文件
        
        Returns:
            Excel 文件名列表
        """
        if not os.path.exists(self.excel_directory):
            os.makedirs(self.excel_directory)
            return []
            
        excel_files = []
        for file in os.listdir(self.excel_directory):
            if any(file.lower().endswith(ext) for ext in self.supported_extensions):
                excel_files.append(file)
        
        return sorted(excel_files)
    
    def get_sheet_names(self, filename: str) -> List[str]:
        """
        获取 Excel 文件中所有工作表名称
        
        Args:
            filename: Excel 文件名
            
        Returns:
            工作表名称列表
        """
        file_path = os.path.join(self.excel_directory, filename)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件 {filename} 不存在")
            
        try:
            # 使用 pandas 读取 Excel 文件的所有工作表名称
            excel_file = pd.ExcelFile(file_path)
            return excel_file.sheet_names
        except Exception as e:
            raise Exception(f"读取文件 {filename} 失败: {str(e)}")
    
    def read_sheet_data(self, filename: str, sheet_name: str, 
                       max_rows: int = 50, max_cols: int = 20) -> Dict[str, Any]:
        """
        读取指定工作表的数据
        
        Args:
            filename: Excel 文件名
            sheet_name: 工作表名称
            max_rows: 最大显示行数
            max_cols: 最大显示列数
            
        Returns:
            包含表格数据和元信息的字典
        """
        file_path = os.path.join(self.excel_directory, filename)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件 {filename} 不存在")
            
        try:
            # 读取指定工作表
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 获取原始数据信息
            total_rows, total_cols = df.shape
            
            # 限制显示的行列数
            display_df = df.iloc[:max_rows, :max_cols]
            
            # 处理 NaN 值
            display_df = display_df.fillna('')
            
            # 转换为字典格式
            data = {
                'filename': filename,
                'sheet_name': sheet_name,
                'total_rows': total_rows,
                'total_cols': total_cols,
                'displayed_rows': min(max_rows, total_rows),
                'displayed_cols': min(max_cols, total_cols),
                'columns': list(display_df.columns),
                'data': display_df.to_dict('records'),
                'preview_note': f"显示前 {min(max_rows, total_rows)} 行，前 {min(max_cols, total_cols)} 列"
            }
            
            return data
            
        except Exception as e:
            raise Exception(f"读取工作表 {sheet_name} 失败: {str(e)}")
    
    def search_in_sheet(self, filename: str, sheet_name: str, 
                       search_term: str, max_results: int = 10) -> Dict[str, Any]:
        """
        在工作表中搜索特定内容
        
        Args:
            filename: Excel 文件名
            sheet_name: 工作表名称
            search_term: 搜索关键词
            max_results: 最大结果数
            
        Returns:
            搜索结果
        """
        file_path = os.path.join(self.excel_directory, filename)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件 {filename} 不存在")
            
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            df = df.fillna('')
            
            # 搜索包含关键词的行
            mask = df.astype(str).apply(
                lambda x: x.str.contains(search_term, case=False, na=False)
            ).any(axis=1)
            
            matching_rows = df[mask].head(max_results)
            
            results = {
                'filename': filename,
                'sheet_name': sheet_name,
                'search_term': search_term,
                'total_matches': mask.sum(),
                'displayed_matches': len(matching_rows),
                'columns': list(df.columns),
                'matching_data': matching_rows.to_dict('records')
            }
            
            return results
            
        except Exception as e:
            raise Exception(f"搜索失败: {str(e)}")
    
    def get_sheet_summary(self, filename: str, sheet_name: str) -> Dict[str, Any]:
        """
        获取工作表的摘要信息
        
        Args:
            filename: Excel 文件名
            sheet_name: 工作表名称
            
        Returns:
            工作表摘要信息
        """
        file_path = os.path.join(self.excel_directory, filename)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件 {filename} 不存在")
            
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 基本信息
            summary = {
                'filename': filename,
                'sheet_name': sheet_name,
                'total_rows': len(df),
                'total_cols': len(df.columns),
                'columns': list(df.columns),
                'data_types': df.dtypes.to_dict(),
                'non_null_counts': df.count().to_dict(),
                'has_numeric_data': any(df.select_dtypes(include=['number']).columns),
                'first_few_rows': df.head(3).fillna('').to_dict('records')
            }
            
            # 如果有数值列，提供统计信息
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                summary['numeric_summary'] = df[numeric_cols].describe().to_dict()
            
            return summary
            
        except Exception as e:
            raise Exception(f"获取摘要失败: {str(e)}")
